# 异步导入API接口设计文档

## 📋 **任务2.1完成报告：设计异步导入服务接口**

**任务名称**: 设计异步导入服务接口  
**计划时间**: 0.5天  
**实际用时**: 按计划完成  
**完成状态**: ✅ **已完成**

## 🎯 **设计原则**

### **1. 与现有接口兼容**
- 保持与现有 `/reg/customerReg/importExcel` 接口相同的参数结构
- 复用现有的文件验证逻辑和错误处理机制
- 保持相同的权限控制和用户认证方式

### **2. RESTful API设计**
- 使用标准的HTTP方法和状态码
- 清晰的资源路径命名
- 统一的响应格式

### **3. 异步处理模式**
- 立即返回任务ID，不阻塞客户端
- 提供进度查询接口
- 支持任务控制操作（取消、重试）

## 🔧 **核心接口设计**

### **1. 异步导入接口**

#### **接口信息**
```http
POST /reg/async-import/excel
Content-Type: multipart/form-data
```

#### **请求参数**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| file | MultipartFile | 是 | Excel文件 |
| companyRegId | String | 是 | 单位登记ID |
| options | String | 否 | 导入选项（JSON格式） |

#### **响应格式**
```json
{
  "success": true,
  "result": "task-uuid-12345",
  "message": "导入任务已创建，正在处理中",
  "code": 200,
  "timestamp": 1703923200000
}
```

#### **业务逻辑**
1. **参数验证**: 文件格式、大小、单位ID有效性
2. **权限检查**: 用户登录状态和操作权限
3. **并发控制**: 检查是否有正在进行的导入任务
4. **任务创建**: 生成唯一任务ID，创建任务记录
5. **异步处理**: 启动后台导入线程
6. **立即响应**: 返回任务ID给客户端

### **2. 进度查询接口**

#### **接口信息**
```http
GET /reg/async-import/progress/{taskId}
```

#### **响应格式**
```json
{
  "success": true,
  "result": {
    "taskId": "task-uuid-12345",
    "status": "PROCESSING",
    "progress": 65,
    "totalCount": 1000,
    "processedCount": 650,
    "successCount": 620,
    "failureCount": 30,
    "currentMessage": "正在处理第650条记录...",
    "startTime": 1703923200000,
    "duration": 120000
  }
}
```

### **3. 任务控制接口**

#### **取消任务**
```http
POST /reg/async-import/cancel/{taskId}
```

#### **重试任务**
```http
POST /reg/async-import/retry/{taskId}
```

#### **获取结果**
```http
GET /reg/async-import/result/{taskId}
```

### **4. 文件下载接口**

#### **错误报告下载**
```http
GET /reg/async-import/error-report/{taskId}
```

#### **成功记录导出**
```http
GET /reg/async-import/export-success/{taskId}
```

## 📊 **接口对比分析**

### **现有同步接口 vs 新异步接口**

| 特性 | 同步接口 | 异步接口 |
|------|----------|----------|
| **响应时间** | 文件大小决定（可能超时） | 立即响应（<2秒） |
| **用户体验** | 等待期间无反馈 | 实时进度反馈 |
| **文件大小限制** | 受HTTP超时限制 | 支持大文件（50MB+） |
| **并发处理** | 阻塞式，影响其他操作 | 非阻塞，可并行操作 |
| **错误处理** | 失败需重新上传 | 支持重试和断点续传 |
| **监控能力** | 无法监控进度 | 完整的监控和日志 |

### **参数兼容性**

#### **现有接口参数**
```http
POST /reg/customerReg/importExcel?companyRegId=xxx
Content-Type: multipart/form-data
file: [Excel文件]
```

#### **新异步接口参数**
```http
POST /reg/async-import/excel
Content-Type: multipart/form-data
file: [Excel文件]
companyRegId: xxx
options: {"skipDuplicate": true, "validateOnly": false}
```

**兼容性保证**:
- ✅ 保持相同的必填参数
- ✅ 新增的可选参数不影响现有调用
- ✅ 文件处理逻辑完全复用

## 🔒 **安全设计**

### **1. 认证和授权**
```java
// 用户认证检查
LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
if (sysUser == null) {
    return Result.error("用户未登录");
}

// 权限检查（复用现有权限体系）
// 通过Spring Security和Shiro进行权限控制
```

### **2. 文件安全验证**
```java
// 文件类型验证
String fileName = file.getOriginalFilename();
if (!fileName.toLowerCase().endsWith(".xlsx") && 
    !fileName.toLowerCase().endsWith(".xls")) {
    return Result.error("只支持Excel文件格式");
}

// 文件大小限制
long maxFileSize = 50 * 1024 * 1024; // 50MB
if (file.getSize() > maxFileSize) {
    return Result.error("文件大小不能超过50MB");
}
```

### **3. 并发控制**
```java
// 检查是否有正在处理的任务
if (importTaskService.hasProcessingTask(companyRegId)) {
    return Result.error("该单位正在进行导入操作，请等待完成后再试");
}
```

## 🎨 **错误处理设计**

### **1. 统一错误响应格式**
```json
{
  "success": false,
  "result": null,
  "message": "具体错误信息",
  "code": 500,
  "timestamp": 1703923200000,
  "errorCode": "IMPORT_FILE_TOO_LARGE",
  "details": {
    "maxSize": "50MB",
    "actualSize": "75MB"
  }
}
```

### **2. 错误码定义**
| 错误码 | HTTP状态码 | 说明 |
|--------|------------|------|
| IMPORT_FILE_EMPTY | 400 | 上传文件为空 |
| IMPORT_FILE_TOO_LARGE | 400 | 文件大小超限 |
| IMPORT_INVALID_FORMAT | 400 | 文件格式不支持 |
| IMPORT_COMPANY_NOT_FOUND | 404 | 单位不存在 |
| IMPORT_TASK_RUNNING | 409 | 已有任务在运行 |
| IMPORT_TASK_NOT_FOUND | 404 | 任务不存在 |
| IMPORT_PERMISSION_DENIED | 403 | 权限不足 |

### **3. 异常处理策略**
```java
try {
    // 业务逻辑
    return Result.OK("操作成功", result);
} catch (BusinessException e) {
    log.warn("业务异常: {}", e.getMessage());
    return Result.error(e.getMessage());
} catch (Exception e) {
    log.error("系统异常", e);
    return Result.error("系统繁忙，请稍后重试");
}
```

## 📈 **性能设计**

### **1. 响应时间目标**
- **任务创建**: <2秒
- **进度查询**: <500ms
- **任务控制**: <1秒
- **文件下载**: 根据文件大小

### **2. 并发处理能力**
- **同时导入任务**: 5个
- **API并发请求**: 100+/秒
- **WebSocket连接**: 100+

### **3. 资源使用优化**
```java
// 文件流处理优化
try (InputStream inputStream = file.getInputStream()) {
    // 使用流式处理，避免一次性加载到内存
    list = ExcelImportUtil.importExcel(inputStream, CustomerReg.class, params);
}

// 批量数据库操作
@Transactional(rollbackFor = Exception.class)
public void batchSaveCustomerRegs(List<CustomerReg> customerRegs) {
    // 批量插入，提高性能
    this.saveBatch(customerRegs, 1000);
}
```

## 🔄 **向后兼容策略**

### **1. 渐进式升级**
- 保持现有同步接口不变
- 新增异步接口作为增强功能
- 前端可选择使用哪种方式

### **2. 配置开关控制**
```yaml
async:
  import:
    enabled: true  # 是否启用异步导入
    fallback-to-sync: true  # 异步失败时是否回退到同步
```

### **3. 数据格式兼容**
- 复用现有的Excel模板格式
- 保持相同的数据验证规则
- 错误信息格式保持一致

## 🚀 **扩展性设计**

### **1. 插件化导入类型**
```java
public interface ImportTypeHandler {
    String getType();
    void processImport(String taskId, MultipartFile file, JSONObject params);
}

// 支持不同类型的导入
@Component
public class CustomerRegImportHandler implements ImportTypeHandler {
    public String getType() { return "customer_reg"; }
    // 实现具体导入逻辑
}
```

### **2. 配置化验证规则**
```json
{
  "validationRules": {
    "required": ["name", "idCard"],
    "format": {
      "idCard": "^[1-9]\\d{17}[0-9Xx]$",
      "phone": "^1[3-9]\\d{9}$"
    },
    "length": {
      "name": {"min": 2, "max": 50}
    }
  }
}
```

### **3. 多租户支持**
```java
// 支持多租户隔离
public class TenantAwareAsyncImportService {
    public void processImportAsync(String tenantId, String taskId, ...) {
        // 租户隔离逻辑
    }
}
```

## ✅ **任务2.1完成总结**

### **主要成果**
1. ✅ **完整的API接口设计**: 8个核心接口，覆盖完整业务流程
2. ✅ **与现有系统兼容**: 参数格式、权限体系、错误处理完全兼容
3. ✅ **安全性保障**: 认证、授权、文件验证、并发控制
4. ✅ **性能优化**: 异步处理、批量操作、资源管理
5. ✅ **扩展性设计**: 插件化、配置化、多租户支持

### **技术亮点**
- 🌟 **RESTful设计**: 标准化的API设计，易于理解和使用
- 🌟 **异步模式**: 立即响应 + 实时进度，用户体验极佳
- 🌟 **完整生命周期**: 创建→进度→控制→结果，闭环管理
- 🌟 **向后兼容**: 不影响现有功能，渐进式升级

### **下一步准备**
- 🎯 **任务2.2**: 实现异步导入服务
- 🎯 基于设计的接口规范进行具体实现
- 🎯 集成现有的导入业务逻辑
- 🎯 添加进度回调和错误处理

**任务2.1状态**: ✅ **圆满完成**  
**准备状态**: 🚀 **已准备好进行任务2.2**
