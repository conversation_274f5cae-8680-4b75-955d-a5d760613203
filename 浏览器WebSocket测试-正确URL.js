// 在浏览器控制台中运行此脚本来测试WebSocket连接
// 使用正确的后端URL: localhost:8090/jeecgboot

// 1. 测试健康检查接口
async function testHealthCheck() {
    console.log('🏥 测试健康检查接口...');
    
    try {
        const response = await fetch('http://localhost:8090/jeecgboot/reg/async-import/health');
        const result = await response.json();
        console.log('✅ 健康检查成功:', result);
        return true;
    } catch (error) {
        console.error('❌ 健康检查失败:', error);
        return false;
    }
}

// 2. 测试进度事件接口
async function testProgressEvent(taskId = 'test-' + Date.now()) {
    console.log('🧪 测试进度事件接口，任务ID:', taskId);
    
    try {
        const response = await fetch(`http://localhost:8090/jeecgboot/reg/async-import/test-progress/${taskId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        });
        
        const result = await response.json();
        console.log('✅ 进度事件测试成功:', result);
        return taskId;
    } catch (error) {
        console.error('❌ 进度事件测试失败:', error);
        return null;
    }
}

// 3. 测试原生WebSocket连接
function testNativeWebSocket() {
    console.log('🔌 测试原生WebSocket连接...');
    
    try {
        // 注意：原生WebSocket使用ws://协议，不是http://
        const ws = new WebSocket('ws://localhost:8090/jeecgboot/ws/import-progress');
        
        ws.onopen = function(event) {
            console.log('✅ 原生WebSocket连接成功!', event);
        };
        
        ws.onmessage = function(event) {
            console.log('📨 收到原生WebSocket消息:', event.data);
        };
        
        ws.onerror = function(error) {
            console.error('❌ 原生WebSocket错误:', error);
        };
        
        ws.onclose = function(event) {
            console.log('🔌 原生WebSocket连接关闭:', event.code, event.reason);
        };
        
        // 保存到全局变量
        window.testWs = ws;
        
    } catch (error) {
        console.error('❌ 创建原生WebSocket失败:', error);
    }
}

// 4. 测试SockJS连接（如果页面中有SockJS库）
function testSockJSConnection() {
    console.log('🧦 测试SockJS连接...');
    
    if (typeof SockJS === 'undefined') {
        console.warn('⚠️ SockJS库未加载，跳过SockJS测试');
        return;
    }
    
    try {
        const socket = new SockJS('http://localhost:8090/jeecgboot/ws/import-progress');
        
        socket.onopen = function() {
            console.log('✅ SockJS连接成功!');
        };
        
        socket.onmessage = function(e) {
            console.log('📨 收到SockJS消息:', e.data);
        };
        
        socket.onclose = function() {
            console.log('🔌 SockJS连接关闭');
        };
        
        socket.onerror = function(error) {
            console.error('❌ SockJS连接错误:', error);
        };
        
        // 保存到全局变量
        window.testSockJS = socket;
        
    } catch (error) {
        console.error('❌ 创建SockJS连接失败:', error);
    }
}

// 5. 完整测试流程
async function runFullTest() {
    console.log('🚀 开始完整的WebSocket测试流程...');
    console.log('后端地址: http://localhost:8090/jeecgboot');
    
    // 1. 测试健康检查
    const healthOk = await testHealthCheck();
    if (!healthOk) {
        console.error('❌ 健康检查失败，请检查后端服务是否启动');
        return;
    }
    
    // 2. 测试进度事件
    const taskId = await testProgressEvent();
    if (!taskId) {
        console.error('❌ 进度事件测试失败');
        return;
    }
    
    // 3. 测试WebSocket连接
    console.log('📡 开始测试WebSocket连接...');
    testNativeWebSocket();
    
    // 等待一秒后测试SockJS
    setTimeout(() => {
        testSockJSConnection();
    }, 1000);
    
    console.log('✅ 测试流程完成！请查看上面的日志结果。');
}

// 6. 使用说明
console.log(`
=== WebSocket测试脚本 ===
后端地址: http://localhost:8090/jeecgboot

可用命令:
1. runFullTest()           - 运行完整测试
2. testHealthCheck()       - 测试健康检查
3. testProgressEvent()     - 测试进度事件
4. testNativeWebSocket()   - 测试原生WebSocket
5. testSockJSConnection()  - 测试SockJS连接

推荐先运行: runFullTest()
`);

// 导出函数到全局作用域
window.testHealthCheck = testHealthCheck;
window.testProgressEvent = testProgressEvent;
window.testNativeWebSocket = testNativeWebSocket;
window.testSockJSConnection = testSockJSConnection;
window.runFullTest = runFullTest;
