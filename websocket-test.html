<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket连接测试</title>
    <script src="https://cdn.jsdelivr.net/npm/sockjs-client@1.6.1/dist/sockjs.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@stomp/stompjs@7.0.0/bundles/stomp.umd.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .status { padding: 10px; margin: 10px 0; border-radius: 4px; }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .log { background-color: #f8f9fa; border: 1px solid #dee2e6; padding: 10px; height: 300px; overflow-y: auto; font-family: monospace; font-size: 12px; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; }
        input { padding: 8px; margin: 5px; width: 300px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>WebSocket连接测试</h1>
        
        <div id="status" class="status info">未连接</div>
        
        <div>
            <button onclick="connectWebSocket()">连接WebSocket</button>
            <button onclick="disconnectWebSocket()">断开连接</button>
            <button onclick="clearLog()">清空日志</button>
        </div>
        
        <div>
            <input type="text" id="taskId" placeholder="输入任务ID" value="test-task-123">
            <button onclick="subscribeTask()">订阅任务进度</button>
            <button onclick="testProgress()">测试进度事件</button>
        </div>
        
        <h3>连接日志</h3>
        <div id="log" class="log"></div>
    </div>

    <script>
        let stompClient = null;
        let connected = false;
        let subscription = null;

        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
        }

        function connectWebSocket() {
            try {
                log('开始连接WebSocket...');
                updateStatus('正在连接...', 'info');
                
                // 创建SockJS连接
                const socket = new SockJS('http://localhost:8080/ws/import-progress');
                stompClient = Stomp.over(socket);
                
                // 启用调试日志
                stompClient.debug = function(str) {
                    log('STOMP: ' + str);
                };

                stompClient.connect({}, function(frame) {
                    log('WebSocket连接成功: ' + frame);
                    updateStatus('已连接', 'success');
                    connected = true;
                    
                    // 自动订阅通用频道
                    stompClient.subscribe('/topic/import-progress', function(message) {
                        log('收到通用消息: ' + message.body);
                        try {
                            const data = JSON.parse(message.body);
                            log('解析后的数据: ' + JSON.stringify(data, null, 2));
                        } catch (e) {
                            log('消息解析失败: ' + e.message);
                        }
                    });
                    
                }, function(error) {
                    log('WebSocket连接失败: ' + error);
                    updateStatus('连接失败', 'error');
                    connected = false;
                });
                
            } catch (error) {
                log('创建WebSocket连接失败: ' + error);
                updateStatus('连接失败', 'error');
            }
        }

        function disconnectWebSocket() {
            if (stompClient && connected) {
                stompClient.disconnect(function() {
                    log('WebSocket连接已断开');
                    updateStatus('已断开', 'info');
                    connected = false;
                });
            }
        }

        function subscribeTask() {
            if (!connected) {
                log('请先连接WebSocket');
                return;
            }
            
            const taskId = document.getElementById('taskId').value;
            if (!taskId) {
                log('请输入任务ID');
                return;
            }
            
            // 取消之前的订阅
            if (subscription) {
                subscription.unsubscribe();
            }
            
            const destination = '/topic/import-progress/' + taskId;
            log('订阅频道: ' + destination);
            
            subscription = stompClient.subscribe(destination, function(message) {
                log('收到任务消息: ' + message.body);
                try {
                    const data = JSON.parse(message.body);
                    log('任务进度: ' + data.progress + '% - ' + data.message);
                } catch (e) {
                    log('任务消息解析失败: ' + e.message);
                }
            });
        }

        function testProgress() {
            const taskId = document.getElementById('taskId').value;
            if (!taskId) {
                log('请输入任务ID');
                return;
            }
            
            log('发送测试进度事件请求...');
            
            fetch(`http://localhost:8080/reg/async-import/test-progress/${taskId}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                log('测试进度事件响应: ' + JSON.stringify(data));
            })
            .catch(error => {
                log('测试进度事件失败: ' + error);
            });
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }

        // 页面加载时自动连接
        window.onload = function() {
            log('页面加载完成，可以开始测试WebSocket连接');
        };
    </script>
</body>
</html>
