package org.jeecg.modules.reg.service;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.modules.reg.entity.CustomerReg;
import org.jeecg.modules.reg.event.ImportProgressEvent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * @Description: 异步导入执行服务
 * @Author: jeecg-boot
 * @Date: 2024-12-30
 * @Version: V1.0
 */
@Service
@Slf4j
public class AsyncImportExecutorService {

    @Autowired
    private ICustomerRegService customerRegService;
    
    @Autowired
    private ApplicationEventPublisher eventPublisher;

    /**
     * 异步执行导入
     * 
     * @param taskId 任务ID
     */
    @Async("taskExecutor")
    public void executeImportAsync(String companyRegId, List<CustomerReg> regList, String taskId, LoginUser loginUser) {
        try {
            log.info("=== 异步导入服务开始执行 ===");
            log.info("taskId: {}", taskId);
            log.info("当前线程: {}", Thread.currentThread().getName());
            log.info("是否为异步线程: {}", Thread.currentThread().getName().startsWith("Async-"));

            
            // 调用现有的导入方法（现有方法会发布进度事件）
            log.info("开始调用customerRegService.importExcel");
            Result<?> result = customerRegService.dealRegListFromExcle(companyRegId, regList, taskId, loginUser);
            log.info("customerRegService.importExcel调用完成");
            
            if (result.isSuccess()) {
                log.info("=== 异步导入任务完成 ===");
                log.info("taskId: {}", taskId);
                
                // 解析结果数据
                if (result.getResult() instanceof JSONObject) {
                    JSONObject resultData = (JSONObject) result.getResult();
                    int successCount = resultData.getIntValue("successCount");
                    int failureCount = resultData.getIntValue("failureCount");
                    
                    log.info("导入结果 - 成功: {}, 失败: {}", successCount, failureCount);
                    
                    // 发布最终完成事件（如果service中没有发布的话）
                    eventPublisher.publishEvent(ImportProgressEvent.complete(this, taskId, 
                        successCount, failureCount, 
                        String.format("异步导入完成：成功 %d 条，失败 %d 条", successCount, failureCount)));
                }
                
            } else {
                // 发布失败事件
                log.error("=== 异步导入任务失败 ===");
                log.error("taskId: {}, error: {}", taskId, result.getMessage());
                
                eventPublisher.publishEvent(ImportProgressEvent.failed(this, taskId, result.getMessage()));
            }
            
        } catch (Exception e) {
            log.error("=== 异步导入任务执行异常 ===");
            log.error("taskId: {}", taskId, e);
            
            eventPublisher.publishEvent(ImportProgressEvent.failed(this, taskId, 
                "导入执行异常: " + e.getMessage()));
        }
    }
    
    /**
     * 检查异步配置是否正常
     */
    public void checkAsyncConfig() {
        log.info("=== 异步配置检查 ===");
        log.info("当前线程: {}", Thread.currentThread().getName());
        log.info("customerRegService是否为null: {}", customerRegService == null);
        log.info("eventPublisher是否为null: {}", eventPublisher == null);
        log.info("==================");
    }
}
