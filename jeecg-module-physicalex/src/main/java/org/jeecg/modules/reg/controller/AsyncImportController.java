package org.jeecg.modules.reg.controller;

import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.modules.reg.constant.RedisKeyConstants;
import org.jeecg.modules.reg.entity.CustomerReg;
import org.jeecg.modules.reg.event.ImportProgressEvent;
import org.jeecg.modules.reg.service.ICustomerRegService;
import org.jeecg.modules.reg.service.AsyncImportExecutorService;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * @Description: 异步导入控制器
 * @Author: jeecg-boot
 * @Date: 2024-12-30
 * @Version: V1.0
 */
@Api(tags = "异步导入管理")
@RestController
@RequestMapping("/reg/async-import")
@CrossOrigin(origins = {"http://localhost:3200", "http://127.0.0.1:3200"})
@Slf4j
public class AsyncImportController {

    @Autowired
    private ICustomerRegService customerRegService;

    @Autowired
    private ApplicationEventPublisher eventPublisher;

    @Autowired
    private AsyncImportExecutorService asyncImportExecutorService;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    /**
     * 异步导入Excel
     */
    @PostMapping("/import")
    @ApiOperation(value = "异步导入Excel", notes = "基于事件驱动的异步导入Excel文件")
    public Result<String> asyncImportExcel(
            HttpServletRequest request,
            HttpServletResponse response) {

        try {
            // 获取companyRegId参数
            String companyRegId = request.getParameter("companyRegId");
            if (companyRegId == null || companyRegId.trim().isEmpty()) {
                return Result.error("单位登记ID不能为空");
            }

            LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

            List<CustomerReg> list = new ArrayList<>();
            MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
            Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
            for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
                // 获取上传文件对象
                MultipartFile file = entity.getValue();
                ImportParams params = new ImportParams();
                params.setTitleRows(2);
                params.setHeadRows(1);
                params.setNeedSave(true);

                try (InputStream inputStream = file.getInputStream()) {
                    List<CustomerReg> listTemp = ExcelImportUtil.importExcel(inputStream, CustomerReg.class, params);
                    if (CollectionUtils.isNotEmpty(listTemp)) {
                        list.addAll(listTemp);
                    }
                } catch (IOException e) {
                    log.error("读取Excel文件失败", e);
                    continue;
                }
            }

            // 生成任务ID
            String taskId = UUID.randomUUID().toString().replace("-", "");
            log.info("开始异步导入任务: taskId={}, companyRegId={}", taskId, companyRegId);

            // 发布开始事件
            log.info("=== 发布开始事件 ===");
            log.info("taskId: {}", taskId);
            ImportProgressEvent startEvent = ImportProgressEvent.start(this, taskId, 0, "任务已创建，准备开始导入...");
            eventPublisher.publishEvent(startEvent);
            log.info("开始事件已发布");

            // 异步执行导入 - 使用专门的异步服务
            asyncImportExecutorService.executeImportAsync(companyRegId, list, taskId, loginUser);

            return Result.OK("导入任务已创建，正在处理中", taskId);
        } catch (Exception e) {
            log.error("创建异步导入任务失败", e);
            return Result.error("创建导入任务失败: " + e.getMessage());
        }
    }



    /**
     * 获取导入进度
     */
    @GetMapping("/progress/{taskId}")
    @ApiOperation(value = "获取导入进度", notes = "获取指定任务的导入进度信息")
    public Result<JSONObject> getImportProgress(@PathVariable String taskId) {
        try {
            log.info("获取导入进度: taskId={}", taskId);

            // 从Redis获取真实的进度信息 - 使用统一的Key
            String cacheKey = RedisKeyConstants.getImportProgressKey(taskId);
            Object progressObj = redisTemplate.opsForValue().get(cacheKey);

            log.info("查询Redis进度信息: cacheKey={}, 结果={}", cacheKey, progressObj != null ? "有数据" : "无数据");

            if (progressObj != null) {
                // 如果Redis中有进度信息，解析并返回
                String progressStr = progressObj.toString();
                JSONObject progress = JSONObject.parseObject(progressStr);

                log.info("从Redis获取到进度信息: taskId={}, progress={}%", taskId, progress.get("progress"));
                return Result.OK(progress);

            } else {
                // 如果Redis中没有进度信息，返回默认状态
                JSONObject defaultProgress = new JSONObject();
                defaultProgress.put("taskId", taskId);
                defaultProgress.put("eventType", "PROGRESS");
                defaultProgress.put("progress", 0);
                defaultProgress.put("message", "任务正在初始化...");
                defaultProgress.put("totalCount", 0);
                defaultProgress.put("processedCount", 0);
                defaultProgress.put("successCount", 0);
                defaultProgress.put("failureCount", 0);
                defaultProgress.put("timestamp", System.currentTimeMillis());

                log.info("Redis中无进度信息，返回默认状态: taskId={}", taskId);
                return Result.OK(defaultProgress);
            }

        } catch (Exception e) {
            log.error("获取导入进度失败: taskId={}", taskId, e);
            return Result.error("获取进度失败: " + e.getMessage());
        }
    }

    /**
     * 健康检查
     */
    @GetMapping("/health")
    @ApiOperation(value = "健康检查", notes = "检查异步导入服务状态")
    public Result<JSONObject> healthCheck() {
        try {
            // 检查异步配置
            asyncImportExecutorService.checkAsyncConfig();

            JSONObject health = new JSONObject();
            health.put("status", "UP");
            health.put("timestamp", System.currentTimeMillis());
            health.put("service", "AsyncImportService");
            health.put("websocket", "enabled");
            health.put("currentThread", Thread.currentThread().getName());

            return Result.OK(health);
        } catch (Exception e) {
            log.error("健康检查失败", e);
            return Result.error("服务异常: " + e.getMessage());
        }
    }

}
