package org.jeecg.modules.reg.aspect;

import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.jeecg.common.api.vo.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * 导入性能监控切面
 * 监控导入操作的性能指标，包括执行时间、成功率、并发数等
 * 
 * <AUTHOR>
 * @since 2025-08-30
 */
@Slf4j
@Aspect
@Component
public class ImportPerformanceAspect {

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    /**
     * 定义切点：所有导入相关的方法
     */
    @Pointcut("execution(* org.jeecg.modules.reg.service.impl.CustomerRegServiceImpl.dealRegListFromExcle(..)) || " +
              "execution(* org.jeecg.modules.reg.service.impl.CustomerRegServiceImpl.doImportExcel(..))")
    public void importMethods() {}

    /**
     * 环绕通知：监控导入性能
     */
    @Around("importMethods()")
    public Object monitorImportPerformance(ProceedingJoinPoint joinPoint) throws Throwable {
        String methodName = joinPoint.getSignature().getName();
        String className = joinPoint.getTarget().getClass().getSimpleName();
        String operationKey = className + "." + methodName;
        
        // 记录开始时间
        long startTime = System.currentTimeMillis();
        
        // 增加并发计数
        String concurrentKey = "IMPORT_CONCURRENT_COUNT";
        Long concurrentCount = redisTemplate.opsForValue().increment(concurrentKey);
        redisTemplate.expire(concurrentKey, 1, TimeUnit.HOURS);
        
        // 记录性能指标
        ImportMetrics metrics = new ImportMetrics();
        metrics.setOperationKey(operationKey);
        metrics.setStartTime(startTime);
        metrics.setConcurrentCount(concurrentCount.intValue());
        
        try {
            log.info("开始执行导入操作: {}, 当前并发数: {}", operationKey, concurrentCount);
            
            // 执行目标方法
            Object result = joinPoint.proceed();
            
            // 记录成功指标
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            
            metrics.setEndTime(endTime);
            metrics.setDuration(duration);
            metrics.setSuccess(true);
            
            // 分析结果
            analyzeResult(result, metrics);
            
            // 记录性能数据
            recordPerformanceMetrics(metrics);
            
            log.info("导入操作完成: {}, 耗时: {}ms, 成功记录: {}, 失败记录: {}", 
                    operationKey, duration, metrics.getSuccessCount(), metrics.getFailureCount());
            
            return result;
            
        } catch (Throwable e) {
            // 记录失败指标
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            
            metrics.setEndTime(endTime);
            metrics.setDuration(duration);
            metrics.setSuccess(false);
            metrics.setErrorMessage(e.getMessage());
            
            // 记录性能数据
            recordPerformanceMetrics(metrics);
            
            log.error("导入操作失败: {}, 耗时: {}ms, 错误: {}", operationKey, duration, e.getMessage());
            
            throw e;
            
        } finally {
            // 减少并发计数
            redisTemplate.opsForValue().decrement(concurrentKey);
        }
    }

    /**
     * 分析导入结果
     */
    private void analyzeResult(Object result, ImportMetrics metrics) {
        if (result instanceof Result) {
            Result<?> apiResult = (Result<?>) result;
            Object data = apiResult.getResult();
            
            if (data instanceof org.jeecg.excommons.BatchResult) {
                // 处理批量结果
                org.jeecg.excommons.BatchResult<?> batchResult =
                    (org.jeecg.excommons.BatchResult<?>) data;

                metrics.setTotalCount(batchResult.getSuccessCount() + batchResult.getFailureCount());
                metrics.setSuccessCount(batchResult.getSuccessCount());
                metrics.setFailureCount(batchResult.getFailureCount());
            }
        }
    }

    /**
     * 记录性能指标到Redis
     */
    private void recordPerformanceMetrics(ImportMetrics metrics) {
        try {
            // 记录详细指标
            String metricsKey = "IMPORT_METRICS:" + metrics.getOperationKey() + ":" + metrics.getStartTime();
            redisTemplate.opsForValue().set(metricsKey, metrics, 24, TimeUnit.HOURS);
            
            // 更新统计指标
            updateStatistics(metrics);
            
        } catch (Exception e) {
            log.error("记录性能指标失败", e);
        }
    }

    /**
     * 更新统计指标
     */
    private void updateStatistics(ImportMetrics metrics) {
        String statsKey = "IMPORT_STATS:" + metrics.getOperationKey();
        
        // 总调用次数
        redisTemplate.opsForHash().increment(statsKey, "totalCalls", 1);
        
        // 成功次数
        if (metrics.isSuccess()) {
            redisTemplate.opsForHash().increment(statsKey, "successCalls", 1);
        } else {
            redisTemplate.opsForHash().increment(statsKey, "failureCalls", 1);
        }
        
        // 总耗时
        redisTemplate.opsForHash().increment(statsKey, "totalDuration", metrics.getDuration());
        
        // 处理记录数
        redisTemplate.opsForHash().increment(statsKey, "totalRecords", metrics.getTotalCount());
        redisTemplate.opsForHash().increment(statsKey, "successRecords", metrics.getSuccessCount());
        redisTemplate.opsForHash().increment(statsKey, "failureRecords", metrics.getFailureCount());
        
        // 设置过期时间
        redisTemplate.expire(statsKey, 7, TimeUnit.DAYS);
    }

    /**
     * 性能指标数据类
     */
    public static class ImportMetrics {
        private String operationKey;
        private long startTime;
        private long endTime;
        private long duration;
        private boolean success;
        private String errorMessage;
        private int concurrentCount;
        private int totalCount;
        private int successCount;
        private int failureCount;

        // Getter和Setter方法
        public String getOperationKey() { return operationKey; }
        public void setOperationKey(String operationKey) { this.operationKey = operationKey; }
        
        public long getStartTime() { return startTime; }
        public void setStartTime(long startTime) { this.startTime = startTime; }
        
        public long getEndTime() { return endTime; }
        public void setEndTime(long endTime) { this.endTime = endTime; }
        
        public long getDuration() { return duration; }
        public void setDuration(long duration) { this.duration = duration; }
        
        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }
        
        public String getErrorMessage() { return errorMessage; }
        public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }
        
        public int getConcurrentCount() { return concurrentCount; }
        public void setConcurrentCount(int concurrentCount) { this.concurrentCount = concurrentCount; }
        
        public int getTotalCount() { return totalCount; }
        public void setTotalCount(int totalCount) { this.totalCount = totalCount; }
        
        public int getSuccessCount() { return successCount; }
        public void setSuccessCount(int successCount) { this.successCount = successCount; }
        
        public int getFailureCount() { return failureCount; }
        public void setFailureCount(int failureCount) { this.failureCount = failureCount; }
    }
}
