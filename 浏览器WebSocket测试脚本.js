// 在浏览器控制台中运行此脚本来测试WebSocket连接

// 1. 首先加载必要的库（如果页面中没有）
function loadSockJS() {
    return new Promise((resolve, reject) => {
        if (window.SockJS) {
            resolve();
            return;
        }
        
        const script = document.createElement('script');
        script.src = 'https://cdn.jsdelivr.net/npm/sockjs-client@1.6.1/dist/sockjs.min.js';
        script.onload = resolve;
        script.onerror = reject;
        document.head.appendChild(script);
    });
}

function loadStomp() {
    return new Promise((resolve, reject) => {
        if (window.Stomp) {
            resolve();
            return;
        }
        
        const script = document.createElement('script');
        script.src = 'https://cdn.jsdelivr.net/npm/@stomp/stompjs@7.0.0/bundles/stomp.umd.min.js';
        script.onload = resolve;
        script.onerror = reject;
        document.head.appendChild(script);
    });
}

// 2. WebSocket测试函数
async function testWebSocket() {
    console.log('=== 开始WebSocket连接测试 ===');
    
    try {
        // 加载依赖
        await loadSockJS();
        await loadStomp();
        console.log('✅ 依赖库加载完成');
        
        // 创建连接
        const socket = new SockJS('http://localhost:8080/ws/import-progress');
        const stompClient = Stomp.over(socket);
        
        // 启用调试
        stompClient.debug = function(str) {
            console.log('STOMP调试:', str);
        };
        
        // 连接WebSocket
        stompClient.connect({}, function(frame) {
            console.log('✅ WebSocket连接成功:', frame);
            
            // 订阅通用频道
            stompClient.subscribe('/topic/import-progress', function(message) {
                console.log('📨 收到通用消息:', message.body);
                try {
                    const data = JSON.parse(message.body);
                    console.log('📊 解析后的数据:', data);
                } catch (e) {
                    console.error('❌ 消息解析失败:', e);
                }
            });
            
            // 订阅特定任务频道（使用一个测试任务ID）
            const testTaskId = 'test-' + Date.now();
            stompClient.subscribe('/topic/import-progress/' + testTaskId, function(message) {
                console.log('📨 收到任务消息:', message.body);
                try {
                    const data = JSON.parse(message.body);
                    console.log('📊 任务进度:', data.progress + '% - ' + data.message);
                } catch (e) {
                    console.error('❌ 任务消息解析失败:', e);
                }
            });
            
            console.log('✅ 频道订阅完成');
            console.log('🧪 可以调用 testProgressEvent("' + testTaskId + '") 来测试进度事件');
            
            // 将stompClient保存到全局变量，方便后续使用
            window.testStompClient = stompClient;
            window.testTaskId = testTaskId;
            
        }, function(error) {
            console.error('❌ WebSocket连接失败:', error);
        });
        
    } catch (error) {
        console.error('❌ 测试失败:', error);
    }
}

// 3. 测试进度事件
async function testProgressEvent(taskId) {
    if (!taskId) {
        taskId = window.testTaskId || 'test-' + Date.now();
    }
    
    console.log('🧪 测试进度事件，任务ID:', taskId);
    
    try {
        const response = await fetch(`http://localhost:8080/reg/async-import/test-progress/${taskId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        });
        
        const result = await response.json();
        console.log('✅ 进度事件测试响应:', result);
        
    } catch (error) {
        console.error('❌ 进度事件测试失败:', error);
    }
}

// 4. 测试健康检查
async function testHealthCheck() {
    console.log('🏥 测试健康检查...');
    
    try {
        const response = await fetch('http://localhost:8080/reg/async-import/health');
        const result = await response.json();
        console.log('✅ 健康检查响应:', result);
        
    } catch (error) {
        console.error('❌ 健康检查失败:', error);
    }
}

// 5. 使用说明
console.log(`
=== WebSocket测试脚本使用说明 ===

1. 测试WebSocket连接:
   testWebSocket()

2. 测试健康检查:
   testHealthCheck()

3. 测试进度事件:
   testProgressEvent('your-task-id')

4. 开始完整测试:
   testWebSocket().then(() => {
       setTimeout(() => testProgressEvent(), 2000);
   });

请先运行 testWebSocket() 来建立连接！
`);

// 导出函数到全局作用域
window.testWebSocket = testWebSocket;
window.testProgressEvent = testProgressEvent;
window.testHealthCheck = testHealthCheck;
