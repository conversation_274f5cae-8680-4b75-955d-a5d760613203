<template>
  <div class="async-import-button">
    <!-- 导入按钮 -->
    <a-upload :show-upload-list="false" :before-upload="handleBeforeUpload" :accept="accept" :multiple="false">
      <a-button type="primary" :loading="uploading" :disabled="disabled">
        <template #icon>
          <UploadOutlined />
        </template>
        {{ buttonText }}
      </a-button>
    </a-upload>

    <!-- 进度弹窗 -->
    <ImportProgressModal
      v-if="showProgress"
      :task-id="currentTaskId"
      :task-info="currentTaskInfo"
      @close="handleProgressClose"
      @completed="handleImportCompleted"
    />

    <!-- 结果弹窗 -->
    <ImportResultModal v-if="showResult" :result="importResult" @close="handleResultClose" @download-error-report="handleDownloadErrorReport" />

    <!-- 任务历史弹窗 -->
    <ImportHistoryModal v-if="showHistory" :company-reg-id="companyRegId" @close="handleHistoryClose" @view-task="handleViewHistoryTask" />
  </div>
</template>

<script lang="ts" setup>
  import { computed, ref } from 'vue';
  import { message } from 'ant-design-vue';
  import { UploadOutlined } from '@ant-design/icons-vue';
  import ImportProgressModal from './ImportProgressModal.vue';
  import ImportResultModal from './ImportResultModal.vue';
  import ImportHistoryModal from './ImportHistoryModal.vue';
  // import ThreadPoolStatusModal from './ThreadPoolStatusModal.vue'; // 精简版中已移除
  import { importProgressApi } from './ImportProgress.api';

  interface TaskInfo {
    taskId: string;
    fileName: string;
    fileSize: number;
    startTime: number;
    status: string;
  }

  const props = defineProps<{
    companyRegId: string;
    buttonText?: string;
    accept?: string;
    disabled?: boolean;
    showActions?: boolean;
    showSystemActions?: boolean;
    maxFileSize?: number; // MB
  }>();

  const emit = defineEmits<{
    success: [result: any];
    error: [error: any];
    progress: [progress: any];
  }>();

  // 响应式数据
  const uploading = ref(false);
  const showProgress = ref(false);
  const showResult = ref(false);
  const showHistory = ref(false);
  const showSystemStatus = ref(false);

  const currentTaskId = ref('');
  const currentTaskInfo = ref<TaskInfo | null>(null);
  const importResult = ref<any>(null);

  // 计算属性
  const buttonText = computed(() => props.buttonText || '异步导入');
  const accept = computed(() => props.accept || '.xlsx,.xls');
  const maxFileSize = computed(() => (props.maxFileSize || 50) * 1024 * 1024); // 转换为字节

  // 文件上传前的处理
  async function handleBeforeUpload(file: File): Promise<boolean> {
    try {
      // 文件类型验证
      const isExcel =
        file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
        file.type === 'application/vnd.ms-excel' ||
        file.name.toLowerCase().endsWith('.xlsx') ||
        file.name.toLowerCase().endsWith('.xls');

      if (!isExcel) {
        message.error('只能上传Excel文件(.xlsx, .xls)');
        return false;
      }

      // 文件大小验证
      if (file.size > maxFileSize.value) {
        message.error(`文件大小不能超过${props.maxFileSize || 50}MB`);
        return false;
      }

      // 检查系统健康状态 (使用现有接口进行检查)
      try {
        const healthCheck = await importProgressApi.healthCheck();
        if (!healthCheck.success) {
          message.error('系统繁忙，请稍后重试');
          return false;
        }

        // 如果有警告信息，显示给用户
        if (healthCheck.warning) {
          console.warn('Health check warning:', healthCheck.warning);
        }
      } catch (error) {
        console.warn('Health check failed, continuing with upload:', error);
        // 健康检查失败不阻止上传操作
      }

      // 开始异步导入
      await startAsyncImport(file);
      return false; // 阻止默认上传行为
    } catch (error) {
      console.error('文件上传前检查失败:', error);
      message.error('上传失败，请重试');
      return false;
    }
  }

  // 开始异步导入
  async function startAsyncImport(file: File) {
    uploading.value = true;

    try {
      const response = await importProgressApi.asyncImportExcel({
        file,
        companyRegId: props.companyRegId,
      });

      console.log('=== 前端收到响应 ===');
      console.log('响应数据:', response);
      console.log('响应类型:', typeof response);
      console.log('响应结构:', Object.keys(response || {}));

      // 处理不同的响应格式
      const actualResponse = response?.data || response;
      console.log('实际响应数据:', actualResponse);

      if (actualResponse?.success) {
        currentTaskId.value = actualResponse.result;
        currentTaskInfo.value = {
          taskId: actualResponse.result,
          fileName: file.name,
          fileSize: file.size,
          startTime: Date.now(),
          status: 'PENDING',
        };

        //message.success('导入任务已创建，正在处理中...');
        showProgress.value = true;

        emit('progress', {
          taskId: currentTaskId.value,
          status: 'started',
        });
      } else {
        message.error(actualResponse?.message || '创建导入任务失败');
      }
    } catch (error: any) {
      console.error('异步导入失败:', error);
      message.error(error.message || '导入失败，请重试');
      emit('error', error);
    } finally {
      uploading.value = false;
    }
  }

  // 进度弹窗关闭
  function handleProgressClose() {
    showProgress.value = false;
    currentTaskId.value = '';
    currentTaskInfo.value = null;
  }

  // 导入完成
  function handleImportCompleted(result: any) {
    showProgress.value = false;
    importResult.value = result;
    showResult.value = true;

    message.success(`导入完成！成功${result.successCount}条，失败${result.failureCount}条`);
    emit('success', result);
  }

  // 结果弹窗关闭
  function handleResultClose() {
    showResult.value = false;
    importResult.value = null;
  }

  // 下载错误报告
  function handleDownloadErrorReport(taskId: string) {
    importProgressApi.downloadErrorReport(taskId);
  }

  // 显示历史记录
  function handleShowHistory() {
    showHistory.value = true;
  }

  // 历史记录弹窗关闭
  function handleHistoryClose() {
    showHistory.value = false;
  }

  // 查看历史任务
  function handleViewHistoryTask(taskInfo: TaskInfo) {
    currentTaskId.value = taskInfo.taskId;
    currentTaskInfo.value = taskInfo;
    showHistory.value = false;
    showProgress.value = true;
  }

  // 显示系统状态
  function handleShowSystemStatus() {
    showSystemStatus.value = true;
  }

  // 暴露方法给父组件
  defineExpose({
    startImport: (file: File) => startAsyncImport(file),
    showHistory: () => handleShowHistory(),
    showSystemStatus: () => handleShowSystemStatus(),
  });
</script>

<style lang="less" scoped>
  .async-import-button {
    display: inline-flex;
    align-items: center;
  }
</style>
