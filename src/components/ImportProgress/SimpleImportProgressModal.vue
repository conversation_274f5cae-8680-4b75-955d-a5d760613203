<template>
  <a-modal
    v-model:visible="visible"
    title="导入进度"
    :width="600"
    :closable="false"
    :maskClosable="false"
    :footer="null"
    :destroyOnClose="true"
  >
    <div class="import-progress-container">
      <!-- 进度条 -->
      <div class="progress-section">
        <div class="progress-header">
          <h4>处理进度</h4>
          <span class="progress-text">{{ displayProgress }}%</span>
        </div>
        <a-progress
          :percent="displayProgress"
          :status="getProgressStatus()"
          :stroke-color="getProgressColor()"
          :show-info="false"
        />
        <div class="progress-details">
          <a-row :gutter="16">
            <a-col :span="6">
              <a-statistic title="总记录数" :value="displayTotalCount" />
            </a-col>
            <a-col :span="6">
              <a-statistic title="已处理" :value="displayProcessedCount" />
            </a-col>
            <a-col :span="6">
              <a-statistic title="成功" :value="displaySuccessCount" value-style="color: #52c41a" />
            </a-col>
            <a-col :span="6">
              <a-statistic title="失败" :value="displayFailureCount" value-style="color: #ff4d4f" />
            </a-col>
          </a-row>
        </div>
      </div>

      <!-- 当前状态消息 -->
      <div class="status-message" v-if="progressInfo?.message">
        <a-alert :message="progressInfo.message" :type="getAlertType()" show-icon />
      </div>

      <!-- 操作按钮 -->
      <div class="action-buttons">
        <a-space>
          <a-button v-if="canClose" type="primary" @click="handleClose">关闭</a-button>
        </a-space>
      </div>
    </div>
  </a-modal>
</template>

<script lang="ts" setup>
  import { computed, onUnmounted, ref, watch } from 'vue';
  import { message, Statistic } from 'ant-design-vue';
  import { getImportProgress } from '/@/api/asyncImport';

  interface ProgressInfo {
    taskId: string;
    eventType: string;
    progress: number;
    totalCount: number;
    processedCount: number;
    successCount: number;
    failureCount: number;
    message: string;
    errorMessage?: string;
    timestamp: number;
  }

  const props = defineProps<{
    taskId: string;
  }>();

  const emit = defineEmits<{
    close: [];
  }>();

  // 响应式数据
  const visible = ref(true);
  const progressInfo = ref<ProgressInfo | null>(null);
  let pollInterval: NodeJS.Timeout | null = null;

  // 计算属性
  const displayProgress = computed(() => {
    if (!progressInfo.value) return 0;
    const progress = Number(progressInfo.value.progress);
    return isNaN(progress) ? 0 : Math.min(100, Math.max(0, Math.round(progress)));
  });

  const displayTotalCount = computed(() => {
    if (!progressInfo.value) return 0;
    const count = Number(progressInfo.value.totalCount);
    return isNaN(count) ? 0 : Math.max(0, count);
  });

  const displayProcessedCount = computed(() => {
    if (!progressInfo.value) return 0;
    const count = Number(progressInfo.value.processedCount);
    return isNaN(count) ? 0 : Math.max(0, count);
  });

  const displaySuccessCount = computed(() => {
    if (!progressInfo.value) return 0;
    const count = Number(progressInfo.value.successCount);
    return isNaN(count) ? 0 : Math.max(0, count);
  });

  const displayFailureCount = computed(() => {
    if (!progressInfo.value) return 0;
    const count = Number(progressInfo.value.failureCount);
    return isNaN(count) ? 0 : Math.max(0, count);
  });

  const currentStatus = computed(() => {
    if (!progressInfo.value) return 'PENDING';
    
    const eventType = progressInfo.value.eventType;
    switch (eventType) {
      case 'COMPLETE':
      case 'COMPLETED':
        return 'COMPLETED';
      case 'FAILED':
      case 'ERROR':
        return 'FAILED';
      case 'PROGRESS':
      case 'PROCESSING':
      default:
        return 'PROCESSING';
    }
  });

  const canClose = computed(() => {
    const status = currentStatus.value;
    return status === 'COMPLETED' || status === 'FAILED';
  });

  // 监听taskId变化
  watch(
    () => props.taskId,
    (newTaskId) => {
      if (newTaskId) {
        initProgress();
      }
    },
    { immediate: true }
  );

  // 组件卸载时清理
  onUnmounted(() => {
    if (pollInterval) {
      clearInterval(pollInterval);
    }
  });

  // 初始化进度
  function initProgress() {
    // 初始化默认数据
    progressInfo.value = {
      taskId: props.taskId,
      eventType: 'PROGRESS',
      progress: 0,
      totalCount: 0,
      processedCount: 0,
      successCount: 0,
      failureCount: 0,
      message: '正在初始化...',
      timestamp: Date.now(),
    };

    // 启动轮询
    startPolling();
  }

  // 简化的数据解析
  function parseProgressData(data: any): ProgressInfo {
    const parseNumber = (value: any, defaultValue: number = 0): number => {
      const parsed = Number(value);
      return isNaN(parsed) ? defaultValue : Math.max(0, parsed);
    };

    return {
      taskId: data.taskId || props.taskId,
      eventType: data.eventType || 'PROGRESS',
      progress: Math.min(100, Math.max(0, parseNumber(data.progress, 0))),
      totalCount: parseNumber(data.totalCount, 0),
      processedCount: parseNumber(data.processedCount, 0),
      successCount: parseNumber(data.successCount, 0),
      failureCount: parseNumber(data.failureCount, 0),
      message: data.message || '正在处理中...',
      errorMessage: data.errorMessage,
      timestamp: data.timestamp || Date.now(),
    };
  }

  // 轮询获取进度
  function startPolling() {
    console.log('开始轮询进度，taskId:', props.taskId);

    let pollCount = 0;
    const maxPolls = 120; // 最多轮询6分钟

    pollInterval = setInterval(async () => {
      try {
        pollCount++;
        console.log(`轮询第${pollCount}次`);

        const result = await getImportProgress(props.taskId);

        if (result && result.success && result.result) {
          console.log('获取到进度数据:', result.result);
          
          const parsedData = parseProgressData(result.result);
          progressInfo.value = parsedData;

          // 检查是否完成
          if (parsedData.eventType === 'COMPLETE' || parsedData.progress >= 100) {
            console.log('任务完成，停止轮询');
            if (pollInterval) {
              clearInterval(pollInterval);
              pollInterval = null;
            }
            
            if (parsedData.successCount > 0 || parsedData.failureCount > 0) {
              message.success(`导入完成！成功 ${parsedData.successCount} 条，失败 ${parsedData.failureCount} 条`);
            } else {
              message.success('导入完成！');
            }
            return;
          }

          // 检查是否失败
          if (parsedData.eventType === 'FAILED') {
            console.log('任务失败，停止轮询');
            if (pollInterval) {
              clearInterval(pollInterval);
              pollInterval = null;
            }
            message.error(parsedData.errorMessage || '导入失败');
            return;
          }
        }

        // 超时处理
        if (pollCount >= maxPolls) {
          console.log('轮询超时');
          if (pollInterval) {
            clearInterval(pollInterval);
            pollInterval = null;
          }
          message.warning('获取进度超时，请手动刷新查看结果');
        }
      } catch (error) {
        console.error('轮询失败:', error);
      }
    }, 2000); // 每2秒查询一次
  }

  // 工具函数
  function getProgressStatus(): 'success' | 'exception' | 'active' | 'normal' {
    const status = currentStatus.value;
    if (status === 'COMPLETED') return 'success';
    if (status === 'FAILED') return 'exception';
    if (status === 'PROCESSING') return 'active';
    return 'normal';
  }

  function getProgressColor(): string {
    const status = currentStatus.value;
    if (status === 'COMPLETED') return '#52c41a';
    if (status === 'FAILED') return '#ff4d4f';
    return '#1890ff';
  }

  function getAlertType(): 'success' | 'info' | 'warning' | 'error' {
    const status = currentStatus.value;
    if (status === 'COMPLETED') return 'success';
    if (status === 'FAILED') return 'error';
    return 'info';
  }

  function handleClose() {
    visible.value = false;
    emit('close');
  }
</script>

<style lang="less" scoped>
  .import-progress-container {
    .progress-section {
      margin-bottom: 24px;

      .progress-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;

        h4 {
          margin: 0;
        }

        .progress-text {
          font-size: 18px;
          font-weight: bold;
          color: #1890ff;
        }
      }

      .progress-details {
        margin-top: 16px;
      }
    }

    .status-message {
      margin-bottom: 16px;
    }

    .action-buttons {
      text-align: center;
    }
  }
</style>
