<template>
  <div class="p-2">
    <!--查询区域-->
    <div class="jeecg-basic-table-form-container">
      <a-form ref="formRef" @keyup.enter="searchQuery" :model="queryParam" :label-col="labelCol" :wrapper-col="wrapperCol">
        <a-row :gutter="24">
          <!--          <a-col :lg="6">
            <a-form-item name="examCategory">
              <template #label><span title="体检分类">体检分类</span></template>
              <j-dict-select-tag placeholder="请选择体检分类" v-model:value="queryParam.examCategory" dictCode="examination_type" />
            </a-form-item>
          </a-col>-->
          <a-col :lg="6">
            <a-form-item name="name">
              <template #label><span title="姓名">姓名</span></template>
              <a-input placeholder="请输入姓名" v-model:value="queryParam.name" />
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item name="status">
              <template #label><span title="登记状态">登记状态</span></template>
              <j-dict-select-tag dict-code="regStatus" size="middle" placeholder="登记状态" v-model:value="queryParam.status" style="width: 100%" />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item name="companyNotifyFlag">
              <template #label><span title="团检通知状态">团检通知状态</span></template>
              <a-select v-model:value="queryParam.companyNotifyFlag">
                <a-select-option value="">全部</a-select-option>
                <a-select-option value="待发送">待发送</a-select-option>
                <a-select-option value="已发送">已发送</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <template v-if="toggleSearchStatus">
            <!--            <a-col :lg="6">
              <a-form-item name="cardType">
                <template #label><span title="证件类型">证件类型</span></template>
                <j-dict-select-tag placeholder="请选择证件类型" v-model:value="queryParam.cardType" dictCode="idcard_type" />
              </a-form-item>
            </a-col>-->
            <a-col :lg="6">
              <a-form-item name="idCard">
                <template #label><span title="证件号">证件号</span></template>
                <a-input placeholder="请输入证件号" v-model:value="queryParam.idCard" />
              </a-form-item>
            </a-col>
            <a-col :lg="6">
              <a-form-item name="gender">
                <template #label><span title="性别">性别</span></template>
                <j-dict-select-tag placeholder="请选择性别" v-model:value="queryParam.gender" dictCode="sex" />
              </a-form-item>
            </a-col>
            <a-col :lg="6">
              <a-form-item name="phone">
                <template #label><span title="电话">电话</span></template>
                <a-input placeholder="请输入电话" v-model:value="queryParam.phone" />
              </a-form-item>
            </a-col>
            <a-col :lg="6">
              <a-form-item name="worKtype">
                <template #label><span title="工种">工种</span></template>
                <j-async-search-select placeholder="请选择工种" v-model:value="queryParam.worKtype" dict="zy_worktype,name,id" />
              </a-form-item>
            </a-col>
            <a-col :lg="6">
              <a-form-item name="workShop">
                <template #label><span title="车间">车间</span></template>
                <j-async-search-select placeholder="请选择车间" v-model:value="queryParam.workShop" dict="zy_work_shop,name,id" />
              </a-form-item>
            </a-col>
          </template>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <span style="float: left; overflow: hidden" class="table-page-search-submitButtons">
              <a-col :lg="6">
                <a-button type="primary" preIcon="ant-design:search-outlined" @click="searchQuery">查询</a-button>
                <a-button type="primary" preIcon="ant-design:reload-outlined" @click="searchReset" style="margin-left: 8px">重置</a-button>
                <a @click="toggleSearchStatus = !toggleSearchStatus" style="margin-left: 8px">
                  {{ toggleSearchStatus ? '收起' : '展开' }}
                  <Icon :icon="toggleSearchStatus ? 'ant-design:up-outlined' : 'ant-design:down-outlined'" />
                </a>
              </a-col>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <!--引用表格-->
    <BasicTable @register="registerTable" :rowSelection="rowSelection">
      <!--插槽:table标题-->
      <template #tableTitle>
        <a-button type="primary" @click="handleAdd" preIcon="ant-design:plus-outlined"> 新增</a-button>
        <a-button type="primary" preIcon="ant-design:export-outlined" @click="onExportXls"> 下载导入模板</a-button>
        <!-- 异步导入按钮 -->
        <AsyncImportButton
          :company-reg-id="companyReg.value.id"
          button-text="异步导入"
          :show-actions="true"
          :show-system-actions="true"
          @success="handleAsyncImportSuccess"
          @error="handleAsyncImportError"
          @progress="handleAsyncImportProgress"
        />
        <!-- 保留原有同步导入作为备选 -->
        <j-upload-button type="default" preIcon="ant-design:import-outlined" @click="onImportXls" style="margin-left: 8px"> 导入 </j-upload-button>
        <a-dropdown v-if="selectedRowKeys.length > 0">
          <template #overlay>
            <a-menu>
              <a-menu-item key="1" @click="batchHandleDelete">
                <Icon icon="ant-design:delete-outlined" />
                删除
              </a-menu-item>
            </a-menu>
          </template>
          <a-button
            >批量操作
            <Icon icon="mdi:chevron-down" />
          </a-button>
        </a-dropdown>
        <!-- 高级查询 -->
        <super-query :config="superQueryConfig" @search="handleSuperQuery" />
        <a-button type="primary" @click="handleNotify" preIcon="ant-design:plus-outlined"> 团检通知</a-button>
      </template>
      <!--操作栏-->
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" :dropDownActions="getDropDownAction(record)" />
      </template>
      <template #bodyCell="{ column, record, index, text }"> </template>
    </BasicTable>
    <!-- 表单区域 -->
    <CustomerRegModal ref="registerModal" @success="handleSuccess" />
    <TeamLimitOperationList4CompanyRegModal ref="teamLimitRecordListModal" />
  </div>
</template>

<script lang="ts" name="reg-customerReg" setup>
  import { inject, reactive, ref, watch } from 'vue';
  import { BasicTable, TableAction } from '/@/components/Table';
  import { useListPage } from '/@/hooks/system/useListPage';
  import { columns, superQuerySchema } from './CustomerReg.data';
  import { batchDelete, batchNotify, deleteOne, getExportUrl, getImportUrl, list4CompanyReg } from './CustomerReg.api';
  import CustomerRegModal from './components/CustomerRegModal.vue';
  import { useUserStore } from '/@/store/modules/user';
  import JDictSelectTag from '/@/components/Form/src/jeecg/components/JDictSelectTag.vue';
  import { companyRegIdKey, companyRegKey } from '@/providekey/provideKeys';
  import { JAsyncSearchSelect } from '@/components/Form';
  import { message } from 'ant-design-vue';
  import { useMessage } from '@/hooks/web/useMessage';
  import TeamLimitOperationList4CompanyRegModal from '@/views/reg/components/TeamLimitOperationList4CompanyRegModal.vue';
  // 导入异步导入组件
  import { AsyncImportButton } from '/@/components/ImportProgress';
  import JUploadButton from '@/components/Button/src/JUploadButton.vue';
  const companyReg = inject(companyRegKey, { value: {}, setValue: () => {} });
  const companyRegId = inject(companyRegIdKey, { value: {}, setValue: () => {} });
  const formRef = ref();
  const queryParam = reactive<any>({});
  const toggleSearchStatus = ref<boolean>(false);
  const userStore = useUserStore();
  const registerModal = ref();
  const teamLimitRecordListModal = ref();
  const cRegId = ref('');
  const { createConfirm, createErrorModal } = useMessage();
  watch(
    () => companyReg.value,
    () => {
      if (companyReg.value) {
        reload();
        cRegId.value = companyReg.value.id;
      } else {
        getDataSource.value = [];
        cRegId.value = '';
      }
    },
    { immediate: false }
  );

  //注册table数据
  const { prefixCls, tableContext, onExportXls, onImportXls } = useListPage({
    tableProps: {
      title: '客户登记',
      api: list4CompanyReg,
      columns,
      canResize: false,
      useSearchForm: false,
      size: 'small',
      actionColumn: {
        width: 120,
        fixed: 'right',
      },
      beforeFetch: (params) => {
        queryParam['companyRegId'] = companyReg.value.id || '-99';
        return Object.assign(params, queryParam);
      },
    },
    exportConfig: {
      name: '客户名单模板',
      url: getExportUrl,
      params: {
        templateOnly: '1',
        companyRegId: companyReg.value.id,
      },
    },
    importConfig: {
      url: () => getImportUrl + `?companyRegId=${companyReg.value.id}`,
      success: handleImportSuccess,
    },
  });
  const [registerTable, { reload, collapseAll, updateTableDataRecord, findTableDataRecord, getDataSource }, { rowSelection, selectedRowKeys }] =
    tableContext;
  const labelCol = reactive({
    xs: 24,
    sm: 4,
    xl: 6,
    xxl: 4,
  });
  const wrapperCol = reactive({
    xs: 24,
    sm: 20,
  });

  // 高级查询配置
  const superQueryConfig = reactive(superQuerySchema);

  /**
   * 高级查询事件
   */
  function handleSuperQuery(params) {
    Object.keys(params).map((k) => {
      queryParam[k] = params[k];
    });
    searchQuery();
  }

  /**
   * 新增事件
   */
  function handleAdd() {
    registerModal.value.disableSubmit = false;
    registerModal.value.add();
  }

  /**
   * 编辑事件
   */
  function handleEdit(record: Recordable) {
    registerModal.value.disableSubmit = false;
    registerModal.value.edit(record);
  }

  /**
   * 详情
   */
  function handleDetail(record: Recordable) {
    registerModal.value.disableSubmit = true;
    registerModal.value.edit(record);
  }
  function openLimitRecordModal(record: Recordable) {
    teamLimitRecordListModal.value?.open(record);
  }

  /**
   * 删除事件
   */
  async function handleDelete(record) {
    await deleteOne({ id: record.id }, handleSuccess);
  }

  /**
   * 批量删除事件
   */
  async function batchHandleDelete() {
    await batchDelete({ ids: selectedRowKeys.value }, handleSuccess);
  }
  /**
   * 团检通知
   */
  async function handleNotify() {
    queryParam.regIds = selectedRowKeys;
    batchNotify(queryParam, (res) => {
      if (res.success) {
        message.success('发送成功');
        (selectedRowKeys.value = []) && reload();
      } else {
        message.error(res.message);
      }
    });
  }

  /**
   * 成功回调
   */
  function handleSuccess() {
    (selectedRowKeys.value = []) && reload();
  }

  function handleImportSuccess(res) {
    console.log('========handleImportSuccess======', res);
    if (res.success) {
      let batchRes = res.result;

      let msg = '';
      if (batchRes.failureCount) {
        let failReason = batchRes.failureResults.map((item) => {
          return `${item.item?.name}:${item.reason}`;
        });

        msg = `成功${batchRes.successCount}条，失败${batchRes.failureCount}条。<br/>失败原因：${failReason.join('<br/>')}`;
        createErrorModal({
          title: `导入结果`,
          content: `<div style="max-height: 50vh;overflow-y: auto;">${msg}</div>`,
        });
      } else {
        msg = `成功${batchRes.successCount}条。`;
        message.success(msg);
        handleSuccess();
      }
    } else {
      message.error(res.message);
    }
  }

  /**
   * 异步导入成功处理
   */
  function handleAsyncImportSuccess(result) {
    console.log('========handleAsyncImportSuccess======', result);

    let msg = '';
    if (result.failureCount && result.failureCount > 0) {
      msg = `异步导入完成！成功${result.successCount}条，失败${result.failureCount}条。`;
      message.warning(msg);
    } else {
      msg = `异步导入完成！成功导入${result.successCount}条记录。`;
      message.success(msg);
    }

    // 刷新表格数据
    reload();
  }

  /**
   * 异步导入错误处理
   */
  function handleAsyncImportError(error) {
    console.error('========handleAsyncImportError======', error);
    message.error(error.message || '异步导入失败，请重试');
  }

  /**
   * 异步导入进度处理
   */
  function handleAsyncImportProgress(progress) {}

  /**
   * 操作栏
   */
  function getTableAction(record) {
    return [
      {
        label: '编辑',
        onClick: handleEdit.bind(null, record),
      },
    ];
  }

  /**
   * 下拉操作栏
   */
  function getDropDownAction(record) {
    return [
      {
        label: '详情',
        onClick: handleDetail.bind(null, record),
      },
      {
        label: '删除',
        popConfirm: {
          title: '是否确认删除',
          confirm: handleDelete.bind(null, record),
          placement: 'topLeft',
        },
      },
      {
        label: '额度记录',
        onClick: openLimitRecordModal.bind(null, record),
      },
    ];
  }

  /**
   * 查询
   */
  function searchQuery() {
    reload({ page: 1 });
  }

  /**
   * 重置
   */
  function searchReset() {
    formRef.value.resetFields();
    selectedRowKeys.value = [];
    //刷新数据
    reload();
  }

  watch(
    () => companyRegId.value,
    () => {
      //console.log('------------------companyRegId changed in CustomerRegList4CompanyReg.vue------------------------', companyRegId);
      if (companyRegId.value) {
        reload();
      } else {
        getDataSource.value = [];
      }
    },
    { immediate: false }
  );
</script>

<style lang="less" scoped>
  .jeecg-basic-table-form-container {
    padding: 0;
    .table-page-search-submitButtons {
      display: block;
      margin-bottom: 12px;
      white-space: nowrap;
    }
    .query-group-cust {
      min-width: 100px !important;
    }
    .query-group-split-cust {
      width: 30px;
      display: inline-block;
      text-align: center;
    }
  }
</style>
