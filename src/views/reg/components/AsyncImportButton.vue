<template>
  <div class="async-import-button">
    <!-- 导入按钮 -->
    <a-upload
      :before-upload="handleBeforeUpload"
      :show-upload-list="false"
      accept=".xlsx,.xls"
      :disabled="importing"
    >
      <a-button type="primary" :loading="importing">
        <UploadOutlined />
        {{ importing ? '导入中...' : '异步导入Excel' }}
      </a-button>
    </a-upload>

    <!-- 进度弹窗 -->
    <AsyncImportModal
      v-model:visible="showProgressModal"
      :task-id="currentTaskId"
      @success="handleImportSuccess"
      @cancel="handleImportCancel"
    />
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { UploadOutlined } from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';
import AsyncImportModal from '/@/components/ImportProgress/AsyncImportModal.vue';
import { asyncImportExcel, type ImportProgressInfo } from '/@/api/asyncImport';

const props = defineProps<{
  companyRegId: string;
}>();

const emit = defineEmits(['success', 'error']);

// 状态
const importing = ref(false);
const showProgressModal = ref(false);
const currentTaskId = ref('');

// 文件上传前处理
const handleBeforeUpload = async (file: File) => {
  // 文件类型检查
  const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
                  file.type === 'application/vnd.ms-excel' ||
                  file.name.endsWith('.xlsx') ||
                  file.name.endsWith('.xls');
  
  if (!isExcel) {
    message.error('只能上传Excel文件！');
    return false;
  }

  // 文件大小检查（限制50MB）
  const isLt50M = file.size / 1024 / 1024 < 50;
  if (!isLt50M) {
    message.error('文件大小不能超过50MB！');
    return false;
  }

  // 检查单位ID
  if (!props.companyRegId) {
    message.error('请先选择单位！');
    return false;
  }

  try {
    importing.value = true;
    
    // 调用异步导入API
    const result = await asyncImportExcel({
      file,
      companyRegId: props.companyRegId,
    });

    if (result.success) {
      currentTaskId.value = result.result;
      showProgressModal.value = true;

      console.log('=== 强制显示进度弹窗 ===');
      console.log('taskId:', result.result);
      console.log('showProgressModal:', showProgressModal.value);
      console.log('currentTaskId:', currentTaskId.value);

      // 确保弹窗显示
      setTimeout(() => {
        if (!showProgressModal.value) {
          console.warn('进度弹窗未显示，强制显示');
          showProgressModal.value = true;
        }
      }, 100);

      message.success('导入任务已创建，正在处理中...');
    } else {
      message.error(result.message || '创建导入任务失败');
      emit('error', result.message);
    }
    
  } catch (error) {
    console.error('异步导入失败:', error);
    message.error('创建导入任务失败');
    emit('error', error);
  } finally {
    importing.value = false;
  }

  // 阻止默认上传行为
  return false;
};

// 导入成功处理
const handleImportSuccess = (progressInfo: ImportProgressInfo) => {
  showProgressModal.value = false;
  
  message.success(`导入完成！成功: ${progressInfo.successCount}, 失败: ${progressInfo.failureCount}`);
  
  // 通知父组件刷新数据
  emit('success', {
    taskId: progressInfo.taskId,
    successCount: progressInfo.successCount,
    failureCount: progressInfo.failureCount,
  });
};

// 导入取消处理
const handleImportCancel = (taskId: string) => {
  showProgressModal.value = false;
  message.info('导入已取消');
};
</script>

<style lang="less" scoped>
.async-import-button {
  display: inline-block;
}
</style>
