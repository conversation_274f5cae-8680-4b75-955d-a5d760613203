/**
 * WebSocket配置工具
 * 动态获取WebSocket连接URL，跟随系统环境配置
 */

import { defHttp } from '/@/utils/http/axios';

/**
 * 获取WebSocket连接URL
 * 根据当前环境和axios配置动态生成WebSocket连接地址
 */
export function getWebSocketUrl(): string {
  try {
    // 获取axios的baseURL配置
    const baseURL = defHttp.axiosInstance.defaults.baseURL;
    
    if (baseURL) {
      // 从baseURL中解析出完整的WebSocket URL
      const url = new URL(baseURL, window.location.origin);
      
      // 将http/https协议转换为ws/wss协议
      const wsProtocol = url.protocol === 'https:' ? 'wss:' : 'ws:';
      
      // 构建WebSocket URL
      const wsUrl = `${url.protocol}//${url.host}${url.pathname}/ws/import-progress`;
      
      console.log('从axios配置获取WebSocket URL:', wsUrl);
      return wsUrl;
    }
    
    // 如果没有baseURL配置，使用默认配置
    const defaultUrl = process.env.NODE_ENV === 'development' 
      ? 'http://localhost:8090/jeecgboot/ws/import-progress'
      : '/jeecgboot/ws/import-progress';
    
    console.log('使用默认WebSocket URL:', defaultUrl);
    return defaultUrl;
    
  } catch (error) {
    console.error('获取WebSocket URL失败，使用默认配置:', error);
    
    // 出错时的回退配置
    return process.env.NODE_ENV === 'development' 
      ? 'http://localhost:8090/jeecgboot/ws/import-progress'
      : '/jeecgboot/ws/import-progress';
  }
}

/**
 * 获取API基础URL
 * 用于HTTP请求
 */
export function getApiBaseUrl(): string {
  const baseURL = defHttp.axiosInstance.defaults.baseURL;
  return baseURL || (process.env.NODE_ENV === 'development' 
    ? 'http://localhost:8090/jeecgboot'
    : '/jeecgboot');
}

/**
 * 检查WebSocket连接配置
 * 用于调试和验证
 */
export function checkWebSocketConfig(): void {
  console.log('=== WebSocket配置检查 ===');
  console.log('当前环境:', process.env.NODE_ENV);
  console.log('页面地址:', window.location.href);
  console.log('axios baseURL:', defHttp.axiosInstance.defaults.baseURL);
  console.log('WebSocket URL:', getWebSocketUrl());
  console.log('API基础URL:', getApiBaseUrl());
  console.log('========================');
}
