import SockJS from 'sockjs-client';
import { Stomp, CompatClient } from '@stomp/stompjs';
import { getWebSocketUrl } from '/@/utils/websocket-config';

/**
 * WebSocket连接管理器 - 用于异步导入进度推送
 */
export class ImportProgressWebSocket {
  private stompClient: CompatClient | null = null;
  private connected = false;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectInterval = 3000;
  private subscriptions: Map<string, any> = new Map();



  /**
   * 连接WebSocket
   */
  connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        // 动态获取WebSocket连接URL
        const baseWsUrl = getWebSocketUrl();

        // 获取认证token
        const token = localStorage.getItem('ACCESS_TOKEN') || sessionStorage.getItem('ACCESS_TOKEN');

        // 添加token到URL参数（如果有token的话）
        const wsUrl = token ? `${baseWsUrl}?token=${encodeURIComponent(token)}` : baseWsUrl;

        console.log('WebSocket连接URL:', wsUrl);

        const socket = new SockJS(wsUrl);
        this.stompClient = Stomp.over(() => new SockJS(wsUrl));

        // 禁用调试日志（生产环境）
        this.stompClient.debug = () => {};

        // 连接配置 - 添加认证头
        const connectHeaders = token ? { 'Authorization': `Bearer ${token}` } : {};

        this.stompClient.connect(
          connectHeaders,
          (frame) => {
            console.log('WebSocket连接成功:', frame);
            this.connected = true;
            this.reconnectAttempts = 0;
            resolve();
          },
          (error) => {
            console.error('WebSocket连接失败:', error);
            this.connected = false;
            this.handleReconnect();
            reject(error);
          }
        );
      } catch (error) {
        console.error('创建WebSocket连接失败:', error);
        reject(error);
      }
    });
  }

  /**
   * 断开连接
   */
  disconnect(): void {
    if (this.stompClient && this.connected) {
      // 取消所有订阅
      this.subscriptions.forEach((subscription) => {
        subscription.unsubscribe();
      });
      this.subscriptions.clear();

      // 断开连接
      this.stompClient.disconnect(() => {
        console.log('WebSocket连接已断开');
      });
      
      this.connected = false;
      this.stompClient = null;
    }
  }

  /**
   * 订阅特定任务的进度更新
   */
  subscribeTaskProgress(taskId: string, callback: (progress: any) => void): string {
    if (!this.stompClient || !this.connected) {
      throw new Error('WebSocket未连接');
    }

    const destination = `/topic/import-progress/${taskId}`;
    const subscriptionId = `task-${taskId}`;

    const subscription = this.stompClient.subscribe(destination, (message) => {
      try {
        const progress = JSON.parse(message.body);
        callback(progress);
      } catch (error) {
        console.error('解析进度消息失败:', error);
      }
    });

    this.subscriptions.set(subscriptionId, subscription);
    console.log(`已订阅任务进度: ${taskId}`);
    
    return subscriptionId;
  }

  /**
   * 订阅通用进度更新
   */
  subscribeGeneralProgress(callback: (progress: any) => void): string {
    if (!this.stompClient || !this.connected) {
      throw new Error('WebSocket未连接');
    }

    const destination = '/topic/import-progress';
    const subscriptionId = 'general-progress';

    const subscription = this.stompClient.subscribe(destination, (message) => {
      try {
        const progress = JSON.parse(message.body);
        callback(progress);
      } catch (error) {
        console.error('解析进度消息失败:', error);
      }
    });

    this.subscriptions.set(subscriptionId, subscription);
    console.log('已订阅通用进度更新');
    
    return subscriptionId;
  }

  /**
   * 取消订阅
   */
  unsubscribe(subscriptionId: string): void {
    const subscription = this.subscriptions.get(subscriptionId);
    if (subscription) {
      subscription.unsubscribe();
      this.subscriptions.delete(subscriptionId);
      console.log(`已取消订阅: ${subscriptionId}`);
    }
  }

  /**
   * 处理重连
   */
  private handleReconnect(): void {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      console.log(`尝试重连WebSocket (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`);
      
      setTimeout(() => {
        this.connect().catch((error) => {
          console.error('重连失败:', error);
        });
      }, this.reconnectInterval);
    } else {
      console.error('WebSocket重连次数已达上限，停止重连');
    }
  }

  /**
   * 检查连接状态
   */
  isConnected(): boolean {
    return this.connected;
  }
}

// 创建全局实例
export const importProgressWebSocket = new ImportProgressWebSocket();

// 页面卸载时自动断开连接
window.addEventListener('beforeunload', () => {
  importProgressWebSocket.disconnect();
});
